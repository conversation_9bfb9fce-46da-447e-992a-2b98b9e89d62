import { PopupSpec, usePopup } from "@/components/admin/connectors/Popup";
import { FullLLMProvider, WellKnownLLMProviderDescriptor } from "./interfaces";
import { Modal } from "@/components/Modal";
import { LLMProviderUpdateForm } from "./LLMProviderUpdateForm";
import { CustomLLMProviderUpdateForm } from "./CustomLLMProviderUpdateForm";
import { useState } from "react";
import { LLM_PROVIDERS_ADMIN_URL } from "./constants";
import { mutate } from "swr";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import isEqual from "lodash/isEqual";
import useSWR from "swr";
import { errorHandlingFetcher } from "@/lib/fetcher";
import { TeamGroupedDisplay } from "@/components/admin/TeamGroupedDisplay";
import { useUser } from "@/components/user/UserProvider";
import { UserRole, User, UserTeams } from "@/lib/types";
import { useUserTeams } from "@/lib/hooks";

/**
 * Check if a user can edit/delete an LLM provider.
 * This mirrors the backend logic in onyx.db.llm.can_user_edit_llm_provider
 * - Admin users can edit any provider
 * - Team admin users can only edit providers assigned to their teams (not public ones)
 */
function canUserEditLLMProvider(
  user: User | null,
  provider: FullLLMProvider,
  userTeams: UserTeams[] | undefined
): boolean {
  if (!user) {
    return false;
  }

  if (user.role === UserRole.ADMIN) {
    return true;
  }

  if (user.role === UserRole.TEAM_ADMIN || user.role === UserRole.BASIC) {
    // Team admin can only edit private providers assigned to their teams
    if (provider.is_public) {
      return false; // Cannot edit public providers (created by admin)
    }

    // Check if any of the user's teams are assigned to this provider
    const userTeamIds = new Set((userTeams || []).map(team => team.id));
    const providerTeamIds = new Set(provider.user_teams || []);
    return userTeamIds.size > 0 && providerTeamIds.size > 0 &&
           Array.from(userTeamIds).some(teamId => providerTeamIds.has(teamId));
  }

  return false;
}

function LLMProviderUpdateModal({
  llmProviderDescriptor,
  onClose,
  existingLlmProvider,
  shouldMarkAsDefault,
  setPopup,
}: {
  llmProviderDescriptor: WellKnownLLMProviderDescriptor | null | undefined;
  onClose: () => void;
  existingLlmProvider?: FullLLMProvider;
  shouldMarkAsDefault?: boolean;
  setPopup?: (popup: PopupSpec) => void;
}) {
  const providerName = existingLlmProvider?.name
    ? `"${existingLlmProvider.name}"`
    : llmProviderDescriptor?.display_name ||
      llmProviderDescriptor?.name ||
      "Custom LLM Provider";
  return (
    <Modal
      title={`${llmProviderDescriptor ? "Configure" : "Setup"} ${providerName}`}
      onOutsideClick={() => onClose()}
    >
      <div className="max-h-[70vh] overflow-y-auto px-4">
        {llmProviderDescriptor ? (
          <LLMProviderUpdateForm
            llmProviderDescriptor={llmProviderDescriptor}
            onClose={onClose}
            existingLlmProvider={existingLlmProvider}
            shouldMarkAsDefault={shouldMarkAsDefault}
            setPopup={setPopup}
          />
        ) : (
          <CustomLLMProviderUpdateForm
            onClose={onClose}
            existingLlmProvider={existingLlmProvider}
            shouldMarkAsDefault={shouldMarkAsDefault}
            setPopup={setPopup}
          />
        )}
      </div>
    </Modal>
  );
}

function LLMProviderDisplay({
  llmProviderDescriptor,
  existingLlmProvider,
  shouldMarkAsDefault,
}: {
  llmProviderDescriptor: WellKnownLLMProviderDescriptor | null | undefined;
  existingLlmProvider: FullLLMProvider;
  shouldMarkAsDefault?: boolean;
}) {
  const [formIsVisible, setFormIsVisible] = useState(false);
  const { popup, setPopup } = usePopup();
  const { user } = useUser();
  const { data: userTeams } = useUserTeams();

  // Fetch current default providers for user's teams
  const { data: teamDefaults } = useSWR<Record<string, FullLLMProvider | null>>(
    "/api/admin/llm/provider/default",
    errorHandlingFetcher
  );

  const providerName =
    existingLlmProvider?.name ||
    llmProviderDescriptor?.display_name ||
    llmProviderDescriptor?.name;

  // Check if this provider is set as default for any of the user's teams
  const isDefaultForAnyTeam =
    teamDefaults &&
    Object.values(teamDefaults).some(
      (defaultProvider) => defaultProvider?.id === existingLlmProvider.id
    );

  // Check if user can edit this provider
  const canEdit = canUserEditLLMProvider(user, existingLlmProvider, userTeams);

  return (
    <div>
      {popup}
      <div className="border border-border p-3 dark:bg-neutral-800 dark:border-neutral-700 rounded w-96 flex shadow-md">
        <div className="my-auto">
          <div className="font-bold">{providerName} </div>
          <div className="text-xs italic">({existingLlmProvider.provider})</div>

          {/* Only show "Set as default" if NOT already default */}
          {!isDefaultForAnyTeam && (
            <div
              className="text-xs text-link cursor-pointer pt-1"
              onClick={async () => {
                const response = await fetch(
                  `${LLM_PROVIDERS_ADMIN_URL}/${existingLlmProvider.id}/default`,
                  { method: "POST" }
                );
                if (!response.ok) {
                  const errorMsg = (await response.json()).detail;
                  setPopup({
                    type: "error",
                    message: `Failed to set provider as default: ${errorMsg}`,
                  });
                  return;
                }

                mutate(LLM_PROVIDERS_ADMIN_URL);
                mutate("/api/admin/llm/provider/default");
                setPopup({
                  type: "success",
                  message: `"${providerName}" has been set as the default LLM provider for your team(s)!`,
                });
              }}
            >
              Set as default
            </div>
          )}
        </div>

        {existingLlmProvider && (
          <div className="my-auto ml-3">
            {isDefaultForAnyTeam ? (
              <Badge variant="agent">Default</Badge> // brown shade
            ) : (
              <Badge variant="success">Enabled</Badge>
            )}
          </div>
        )}

        <div className="ml-auto">
          {canEdit && (
            <Button
              variant={existingLlmProvider ? "success-reverse" : "navigate"}
              onClick={() => setFormIsVisible(true)}
            >
              {existingLlmProvider ? "Edit" : "Set up"}
            </Button>
          )}
        </div>
      </div>

      {formIsVisible && (
        <LLMProviderUpdateModal
          llmProviderDescriptor={llmProviderDescriptor}
          onClose={() => setFormIsVisible(false)}
          existingLlmProvider={existingLlmProvider}
          shouldMarkAsDefault={shouldMarkAsDefault}
          setPopup={setPopup}
        />
      )}
    </div>
  );
}


export function ConfiguredLLMProviderDisplay({
  existingLlmProviders,
  llmProviderDescriptors,
}: {
  existingLlmProviders: FullLLMProvider[];
  llmProviderDescriptors: WellKnownLLMProviderDescriptor[];
}) {
  const { user } = useUser();

  // Sort providers by provider name since is_default_provider is no longer available
  const sortedProviders = existingLlmProviders.sort((a, b) => {
    return a.provider > b.provider ? 1 : -1;
  });

  // Render provider cards for a group of providers
  const renderProviderCards = (providers: FullLLMProvider[]) => {
    return (
      <div className="gap-y-4 flex flex-col">
        {providers.map((provider) => {
          const defaultProviderDesciptor = llmProviderDescriptors.find(
            (llmProviderDescriptors) =>
              llmProviderDescriptors.name === provider.provider
          );

          return (
            <LLMProviderDisplay
              key={provider.id}
              // if the user has specified custom model names,
              // then the provider is custom - don't use the default
              // provider descriptor
              llmProviderDescriptor={
                isEqual(provider.model_names, defaultProviderDesciptor?.llm_names)
                  ? defaultProviderDesciptor
                  : null
              }
              existingLlmProvider={provider}
            />
          );
        })}
      </div>
    );
  };

  // Only use team grouping for ADMIN users
  // TEAM_ADMIN users should see the flat list as before
  if (user?.role === UserRole.ADMIN) {
    return (
      <TeamGroupedDisplay
        items={sortedProviders}
        renderContent={renderProviderCards}
        title="LLM Providers"
      />
    );
  }

  // For TEAM_ADMIN users, show the original flat layout
  return renderProviderCards(sortedProviders);
}
