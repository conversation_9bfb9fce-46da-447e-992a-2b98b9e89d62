// Simple test to verify the LLM provider permission logic
// This mimics the canUserEditLL<PERSON>rovider function

const UserRole = {
  ADMIN: "admin",
  TEAM_ADMIN: "team_admin", 
  BASIC: "basic"
};

function canUserEditLLMProvider(user, provider, userTeams) {
  if (!user) {
    return false;
  }

  if (user.role === UserRole.ADMIN) {
    return true;
  }

  if (user.role === UserRole.TEAM_ADMIN || user.role === UserRole.BASIC) {
    // Team admin can only edit private providers assigned to their teams
    if (provider.is_public) {
      return false; // Cannot edit public providers (created by admin)
    }

    // Check if any of the user's teams are assigned to this provider
    const userTeamIds = new Set((userTeams || []).map(team => team.id));
    const providerTeamIds = new Set(provider.user_teams || []);
    return userTeamIds.size > 0 && providerTeamIds.size > 0 && 
           Array.from(userTeamIds).some(teamId => providerTeamIds.has(teamId));
  }

  return false;
}

// Test cases
console.log("Testing LLM Provider Permission Logic");
console.log("=====================================");

// Test 1: Admin user can edit any provider
const adminUser = { role: UserRole.ADMIN };
const publicProvider = { is_public: true, user_teams: [] };
const privateProvider = { is_public: false, user_teams: [1, 2] };

console.log("Test 1 - Admin user:");
console.log("  Can edit public provider:", canUserEditLLMProvider(adminUser, publicProvider, [])); // Should be true
console.log("  Can edit private provider:", canUserEditLLMProvider(adminUser, privateProvider, [])); // Should be true

// Test 2: Team admin cannot edit public providers
const teamAdminUser = { role: UserRole.TEAM_ADMIN };
const userTeams = [{ id: 1 }, { id: 2 }];

console.log("\nTest 2 - Team admin user:");
console.log("  Can edit public provider:", canUserEditLLMProvider(teamAdminUser, publicProvider, userTeams)); // Should be false
console.log("  Can edit private provider (in team):", canUserEditLLMProvider(teamAdminUser, privateProvider, userTeams)); // Should be true

// Test 3: Team admin cannot edit private providers not assigned to their teams
const otherTeamProvider = { is_public: false, user_teams: [3, 4] };
console.log("  Can edit private provider (not in team):", canUserEditLLMProvider(teamAdminUser, otherTeamProvider, userTeams)); // Should be false

// Test 4: Basic user follows same rules as team admin
const basicUser = { role: UserRole.BASIC };
console.log("\nTest 3 - Basic user:");
console.log("  Can edit public provider:", canUserEditLLMProvider(basicUser, publicProvider, userTeams)); // Should be false
console.log("  Can edit private provider (in team):", canUserEditLLMProvider(basicUser, privateProvider, userTeams)); // Should be true

// Test 5: No user
console.log("\nTest 4 - No user:");
console.log("  Can edit any provider:", canUserEditLLMProvider(null, publicProvider, userTeams)); // Should be false

console.log("\n✅ All tests completed!");
